#!/bin/bash

# Server setup script to be run ON the server
# This script should be copied to the server and executed there

set -e

echo "=== Setting up Painting Generator Server ==="

# Update system
echo "Updating system packages..."
apt update && apt upgrade -y

# Install basic dependencies
echo "Installing basic dependencies..."
apt install -y curl wget git nginx ufw

# Install Node.js 18
echo "Installing Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# Verify Node.js installation
echo "Node.js version: $(node --version)"
echo "NPM version: $(npm --version)"

# Install Docker
echo "Installing Docker..."
apt-get remove -y docker docker-engine docker.io containerd runc || true
apt-get update
apt-get install -y ca-certificates curl gnupg lsb-release

# Add Docker's official GPG key
mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# Set up Docker repository
echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

# Install Docker Engine
apt-get update
apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# Start and enable Docker
systemctl start docker
systemctl enable docker

# Install Docker Compose (standalone)
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Verify Docker installation
echo "Docker version: $(docker --version)"
echo "Docker Compose version: $(docker-compose --version)"

# Install PM2 for process management
echo "Installing PM2..."
npm install -g pm2

# Create application directory
echo "Creating application directory..."
mkdir -p /opt/painting-generator
cd /opt/painting-generator

# Create production environment file template
echo "Creating environment template..."
cat > .env.template << 'EOF'
PORT=3000
DB_HOST=mysql
DB_USER=root
DB_PASSWORD=
DB_NAME=painting_generator
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
JWT_SECRET=your_jwt_secret_key_change_this_in_production
SERVER_IP=***************
EOF

# Create PM2 ecosystem file
echo "Creating PM2 configuration..."
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'painting-generator',
    script: 'server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
};
EOF

# Configure basic firewall
echo "Configuring firewall..."
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 3000/tcp
ufw --force enable

echo ""
echo "=== Server setup completed! ==="
echo ""
echo "Next steps:"
echo "1. Upload your application files to /opt/painting-generator"
echo "2. Copy .env.template to .env and update with your API keys"
echo "3. Run: npm install --production"
echo "4. Run: docker-compose up -d mysql"
echo "5. Run: pm2 start ecosystem.config.js"
echo "6. Run: pm2 save && pm2 startup"
echo ""
echo "Your application will be available at: http://***************:3000"
