/**
 * WebSocket service for real-time painting generation updates
 */
class WebSocketService {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.token = null;
    this.eventHandlers = new Map();
  }

  /**
   * Initialize WebSocket connection
   * @param {string} token - JWT token for authentication
   */
  async connect(token) {
    if (this.socket && this.isConnected) {
      return;
    }

    this.token = token;

    try {
      // Get server configuration
      const configResponse = await fetch('/api/config');
      const config = await configResponse.json();
      const serverUrl = `http://${config.serverIP}:${config.apiPort}`;

      this.socket = io(serverUrl, {
        auth: {
          token: token
        },
        transports: ['websocket', 'polling']
      });

      this.setupEventHandlers();
      
      return new Promise((resolve, reject) => {
        this.socket.on('connect', () => {
          console.log('WebSocket connected');
          this.isConnected = true;
          resolve();
        });

        this.socket.on('connect_error', (error) => {
          console.error('WebSocket connection error:', error);
          this.isConnected = false;
          reject(error);
        });

        // Set timeout for connection
        setTimeout(() => {
          if (!this.isConnected) {
            reject(new Error('WebSocket connection timeout'));
          }
        }, 10000);
      });
    } catch (error) {
      console.error('Error connecting to WebSocket:', error);
      throw error;
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  setupEventHandlers() {
    this.socket.on('disconnect', () => {
      console.log('WebSocket disconnected');
      this.isConnected = false;
    });

    this.socket.on('painting_update', (data) => {
      console.log('Received painting update:', data);
      this.emit('painting_update', data);
    });

    this.socket.on('generation_started', (data) => {
      console.log('Received generation started:', data);
      this.emit('generation_started', data);
    });

    this.socket.on('error', (error) => {
      console.error('WebSocket error:', error);
      this.emit('error', error);
    });
  }

  /**
   * Disconnect WebSocket
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }

  /**
   * Add event listener
   * @param {string} event - Event name
   * @param {function} handler - Event handler function
   */
  on(event, handler) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event).push(handler);
  }

  /**
   * Remove event listener
   * @param {string} event - Event name
   * @param {function} handler - Event handler function
   */
  off(event, handler) {
    if (this.eventHandlers.has(event)) {
      const handlers = this.eventHandlers.get(event);
      const index = handlers.indexOf(handler);
      if (index !== -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to registered handlers
   * @param {string} event - Event name
   * @param {any} data - Event data
   */
  emit(event, data) {
    if (this.eventHandlers.has(event)) {
      this.eventHandlers.get(event).forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in event handler for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Check if WebSocket is connected
   */
  isSocketConnected() {
    return this.isConnected && this.socket && this.socket.connected;
  }

  /**
   * Reconnect WebSocket with stored token
   */
  async reconnect() {
    if (this.token) {
      this.disconnect();
      await this.connect(this.token);
    }
  }
}

// Create singleton instance
const websocketService = new WebSocketService();

export default websocketService;
