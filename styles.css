/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background-color: #f5f7fa;
  color: #333;
  line-height: 1.6;
}

h1,
h2,
h3 {
  margin-bottom: 15px;
  color: #2c3e50;
}

/* Layout */
.app-container {
  display: flex;
  min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
  width: 250px;
  background-color: #2c3e50;
  color: #ecf0f1;
  padding: 20px;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #34495e;
}

.title-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.title-item {
  padding: 10px;
  background-color: #34495e;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.title-item:hover {
  background-color: #3d5871;
}

.title-item.active {
  background-color: #3498db;
  font-weight: bold;
}

.empty-state {
  color: #95a5a6;
  text-align: center;
  padding: 15px;
  font-style: italic;
}

/* Main Content Styles */
.main-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

header {
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ddd;
}

/* Reference Images Section */
.reference-section {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 25px;
}

.reference-toggle {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.toggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  margin-right: 10px;
}

.toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #3498db;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.dropzone {
  border: 2px dashed #ddd;
  border-radius: 4px;
  padding: 30px;
  text-align: center;
  margin-bottom: 15px;
  transition: border-color 0.3s, background-color 0.3s;
}

.dropzone:hover,
.dropzone.dragover {
  border-color: #3498db;
  background-color: rgba(52, 152, 219, 0.05);
}

.reference-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.reference-image {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ddd;
  position: relative;
}

.reference-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.remove-image {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 20px;
  height: 20px;
  background-color: #e74c3c;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
}

/* Title Input Section */
.title-input-section {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 25px;
}

.input-group {
  margin-bottom: 15px;
}

.input-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.input-group input,
.input-group select,
.input-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.input-group textarea {
  min-height: 80px;
  resize: vertical;
}

/* Button Styles */
.btn {
  padding: 8px 15px;
  background-color: #95a5a6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn:hover {
  background-color: #7f8c8d;
}

.primary-btn {
  background-color: #3498db;
  padding: 12px 20px;
  font-weight: bold;
}

.primary-btn:hover {
  background-color: #2980b9;
}

/* Progress Section */
.progress-section {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 25px;
}

.progress-item {
  margin-bottom: 15px;
}

.progress-bar {
  height: 10px;
  background-color: #ecf0f1;
  border-radius: 5px;
  overflow: hidden;
  margin-top: 5px;
}

.progress-fill {
  height: 100%;
  background-color: #3498db;
  width: 0%;
  transition: width 0.3s;
}

/* Thumbnails Section */
.thumbnails-section {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.thumbnails-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

/* Painting Card Styles */
.painting-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
}

.painting-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.painting-image-container {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.painting-placeholder {
  text-align: center;
  color: #6c757d;
}

.placeholder-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.placeholder-text {
  font-size: 0.9rem;
  font-weight: 500;
}

.painting-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.painting-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 1rem;
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  transition: opacity 0.2s;
}

.painting-card:hover .painting-actions {
  opacity: 1;
}

.painting-error {
  text-align: center;
  color: #dc3545;
  padding: 1rem;
}

.error-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.error-text {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.error-message {
  font-size: 0.8rem;
  opacity: 0.8;
}

.painting-info {
  padding: 1rem;
}

.painting-summary {
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
  line-height: 1.4;
  color: #333;
}

.painting-progress {
  margin-top: 0.5rem;
}

.progress-text {
  font-size: 0.8rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-percentage {
  font-weight: 600;
  color: #3498db;
}

.painting-progress .progress-bar {
  height: 6px;
  background-color: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.painting-progress .progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  transition: width 0.3s ease;
}

.thumbnail-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.thumbnail-item:hover {
  transform: translateY(-5px);
}

.thumbnail-image {
  width: 100%;
  aspect-ratio: 16/9;
  object-fit: cover;
}

.thumbnail-actions {
  padding: 10px;
  display: flex;
  justify-content: space-between;
}

.action-btn {
  background: none;
  border: none;
  color: #3498db;
  cursor: pointer;
  font-size: 14px;
}

.action-btn:hover {
  text-decoration: underline;
}

/* Loading Animation */
.loading-thumbnail {
  width: 100%;
  aspect-ratio: 16/9;
  background-color: #ecf0f1;
  position: relative;
  overflow: hidden;
}

.loading-thumbnail::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.5),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Individual painting progress */
.painting-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  padding: 8px;
  border-radius: 0 0 8px 8px;
}

.painting-progress-bar {
  width: 100%;
  height: 4px;
  background: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 4px;
}

.painting-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #45a049);
  border-radius: 2px;
  transition: width 0.3s ease;
  width: 0%;
}

.painting-progress-text {
  font-size: 12px;
  color: #666;
  text-align: center;
}

/* Progress states with animations */
.painting-progress.pending .painting-progress-fill {
  background: linear-gradient(90deg, #ffc107, #ff9800);
  animation: pulse 2s infinite;
}

.painting-progress.pending .painting-progress-text {
  color: #ff9800;
}

.painting-progress.creating_prompt .painting-progress-fill {
  background: linear-gradient(90deg, #2196f3, #1976d2);
  animation: pulse 2s infinite;
}

.painting-progress.creating_prompt .painting-progress-text {
  color: #2196f3;
}

.painting-progress.prompt_created .painting-progress-fill {
  background: linear-gradient(90deg, #4caf50, #45a049);
}

.painting-progress.prompt_created .painting-progress-text {
  color: #4caf50;
}

.painting-progress.creating_image .painting-progress-fill {
  background: linear-gradient(90deg, #9c27b0 25%, #e91e63 50%, #9c27b0 75%);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

.painting-progress.creating_image .painting-progress-text {
  color: #9c27b0;
}

.painting-progress.failed .painting-progress-fill {
  background: linear-gradient(90deg, #f44336, #d32f2f);
}

.painting-progress.failed .painting-progress-text {
  color: #f44336;
}

.painting-progress.completed .painting-progress-fill {
  background: linear-gradient(90deg, #4caf50, #45a049);
}

.painting-progress.completed .painting-progress-text {
  color: #4caf50;
}

/* Animations */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

/* Error state for failed paintings */
.thumbnail-error {
  width: 100%;
  height: 200px;
  background: #ffebee;
  border: 2px dashed #f44336;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
}

.error-icon {
  width: 40px;
  height: 40px;
  background: #f44336;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
}

.error-message {
  color: #f44336;
  font-size: 14px;
  margin: 10px 0;
  line-height: 1.4;
}

.thumbnail-error .action-btn {
  background: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-top: 10px;
}

.thumbnail-error .action-btn:hover {
  background: #d32f2f;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    max-height: 200px;
  }
}

.more-thumbnails-section {
  margin-top: 20px;
  text-align: center;
  padding: 15px 0;
  border-top: 1px solid #ecf0f1;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 100;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  overflow: auto;
}

.modal-content {
  background-color: #f5f7fa;
  margin: 5% auto;
  padding: 0;
  width: 80%;
  max-width: 900px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  animation: modalFadeIn 0.3s;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #ddd;
  position: relative;
}

.close-modal {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
  color: #7f8c8d;
}

.close-modal:hover {
  color: #e74c3c;
}

.modal-body {
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.thumbnail-preview {
  flex: 1;
  min-width: 300px;
}

.thumbnail-preview img {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.prompt-details {
  flex: 2;
  min-width: 300px;
}

.prompt-details h4 {
  margin-top: 15px;
  margin-bottom: 5px;
  color: #2c3e50;
  border-bottom: 1px solid #ecf0f1;
  padding-bottom: 5px;
}

.full-prompt {
  background-color: #f1f5f9;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 14px;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ddd;
}

.reference-thumbnails {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.reference-thumb {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
  border: 1px solid #ddd;
}

/* Make thumbnails clickable */
.thumbnail-item {
  cursor: pointer;
}

/* Responsive Modal */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    margin: 10% auto;
  }

  .modal-body {
    flex-direction: column;
  }
}

/* Loading Overlay */
.loading-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  color: white;
}

.loading-overlay p {
  margin-top: 15px;
  font-size: 18px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Auth Form Styles */
.auth-form-container {
  max-width: 500px;
  margin: 50px auto;
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.auth-form-container h1 {
  margin-bottom: 30px;
  color: #3498db;
}

.auth-form-container form {
  text-align: left;
}

.auth-form-container h2 {
  margin-bottom: 20px;
  text-align: center;
}

.auth-form-container .input-group {
  margin-bottom: 20px;
}

.auth-form-container button {
  width: 100%;
  margin-top: 10px;
  margin-bottom: 20px;
}

.auth-form-container p {
  text-align: center;
}

.auth-form-container a {
  color: #3498db;
  text-decoration: none;
}

.auth-form-container a:hover {
  text-decoration: underline;
}

/* User Info in Sidebar */
.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #34495e;
  color: #ecf0f1;
  font-size: 14px;
}

.small-btn {
  padding: 4px 8px;
  font-size: 12px;
}

/* Thumbnail Error State */
.thumbnail-error {
  width: 100%;
  aspect-ratio: 16/9;
  background-color: #f8d7da;
  border-radius: 8px 8px 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
  color: #721c24;
}

.error-icon {
  width: 40px;
  height: 40px;
  background-color: #721c24;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 15px;
}

.error-message {
  margin-bottom: 15px;
  font-size: 14px;
  max-width: 90%;
}
