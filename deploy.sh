#!/bin/bash

# Deployment script for Painting Generator Application
# Target server: root@***************

set -e  # Exit on any error

# Configuration
SERVER_HOST="***************"
SERVER_USER="root"
SERVER_PASSWORD="000000AA"
APP_NAME="painting-generator"
APP_DIR="/opt/$APP_NAME"
DOMAIN="your-domain.com"  # Change this to your actual domain

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to execute commands on remote server
execute_remote() {
    sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" "$1"
}

# Function to copy files to remote server
copy_to_remote() {
    sshpass -p "$SERVER_PASSWORD" scp -o StrictHostKeyChecking=no -r "$1" "$SERVER_USER@$SERVER_HOST:$2"
}

print_status "Starting deployment to $SERVER_HOST..."

# Check if sshpass is installed
if ! command -v sshpass &> /dev/null; then
    print_error "sshpass is required but not installed."
    print_status "Installing sshpass..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        brew install hudochenkov/sshpass/sshpass
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        sudo apt-get update && sudo apt-get install -y sshpass
    else
        print_error "Please install sshpass manually"
        exit 1
    fi
fi

# Test connection
print_status "Testing connection to server..."
if ! execute_remote "echo 'Connection successful'"; then
    print_error "Failed to connect to server"
    exit 1
fi

# Update system and install dependencies
print_status "Updating system and installing dependencies..."
execute_remote "
    apt update && apt upgrade -y
    apt install -y curl wget git nginx ufw
"

# Install Node.js
print_status "Installing Node.js..."
execute_remote "
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt-get install -y nodejs
    node --version
    npm --version
"

# Install Docker and Docker Compose
print_status "Installing Docker and Docker Compose..."
execute_remote "
    apt-get remove -y docker docker-engine docker.io containerd runc || true
    apt-get update
    apt-get install -y ca-certificates curl gnupg lsb-release
    mkdir -p /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg
    echo \"deb [arch=\$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \$(lsb_release -cs) stable\" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    apt-get update
    apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    systemctl start docker
    systemctl enable docker
    docker --version
"

# Install PM2 for process management
print_status "Installing PM2..."
execute_remote "npm install -g pm2"

# Create application directory
print_status "Creating application directory..."
execute_remote "mkdir -p $APP_DIR"

# Copy application files
print_status "Copying application files..."
copy_to_remote "." "$APP_DIR/"

# Set up environment file for production
print_status "Setting up production environment..."
execute_remote "
    cd $APP_DIR
    cp .env .env.backup
    cat > .env << 'EOF'
PORT=3000
DB_HOST=mysql
DB_USER=root
DB_PASSWORD=
DB_NAME=painting_generator
OPENROUTER_API_KEY=
OPENAI_API_KEY=
JWT_SECRET=your_jwt_secret_key_change_this_in_production
SERVER_IP=$SERVER_HOST
EOF
"

# Install Node.js dependencies
print_status "Installing Node.js dependencies..."
execute_remote "
    cd $APP_DIR
    npm install --production
"

# Start MySQL with Docker
print_status "Starting MySQL database..."
execute_remote "
    cd $APP_DIR
    docker compose up -d mysql
    sleep 30  # Wait for MySQL to be ready
"

# Create PM2 ecosystem file
print_status "Creating PM2 configuration..."
execute_remote "
    cd $APP_DIR
    cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: '$APP_NAME',
    script: 'server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
};
EOF
"

# Start application with PM2
print_status "Starting application with PM2..."
execute_remote "
    cd $APP_DIR
    pm2 start ecosystem.config.js
    pm2 save
    pm2 startup
"

print_status "Deployment completed successfully!"
print_status "Application is running on http://$SERVER_HOST:3000"
print_warning "Don't forget to:"
print_warning "1. Update your API keys in $APP_DIR/.env"
print_warning "2. Configure your domain name if needed"
print_warning "3. Set up SSL certificate for production"
print_warning "4. Configure firewall rules"
