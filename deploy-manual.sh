#!/bin/bash

# Manual Deployment Guide for Painting Generator Application
# Server: root@***************
# Password: 000000AA

echo "=== MANUAL DEPLOYMENT GUIDE ==="
echo ""
echo "1. Connect to the server:"
echo "   ssh root@***************"
echo "   Password: 000000AA"
echo ""
echo "2. Update system:"
echo "   apt update && apt upgrade -y"
echo ""
echo "3. Install Node.js:"
echo "   curl -fsSL https://deb.nodesource.com/setup_18.x | bash -"
echo "   apt-get install -y nodejs"
echo ""
echo "4. Install Docker:"
echo "   apt-get install -y docker.io docker-compose"
echo "   systemctl start docker"
echo "   systemctl enable docker"
echo ""
echo "5. Install PM2:"
echo "   npm install -g pm2"
echo ""
echo "6. Create app directory:"
echo "   mkdir -p /opt/painting-generator"
echo "   cd /opt/painting-generator"
echo ""
echo "7. Upload your files using SCP:"
echo "   From your local machine, run:"
echo "   scp -r . root@***************:/opt/painting-generator/"
echo ""
echo "8. On the server, install dependencies:"
echo "   cd /opt/painting-generator"
echo "   npm install --production"
echo ""
echo "9. Update .env file for production:"
echo "   nano .env"
echo "   Change DB_HOST=localhost to DB_HOST=mysql"
echo "   Set SERVER_IP=***************"
echo ""
echo "10. Start MySQL:"
echo "    docker-compose up -d mysql"
echo ""
echo "11. Start the application:"
echo "    pm2 start server.js --name painting-generator"
echo "    pm2 save"
echo "    pm2 startup"
echo ""
echo "12. Configure firewall (optional):"
echo "    ufw allow 22"
echo "    ufw allow 3000"
echo "    ufw enable"
echo ""
echo "Your app will be available at: http://***************:3000"
