const express = require("express");
const cors = require("cors");
const path = require("path");
const http = require("http");
const socketIo = require("socket.io");
const jwt = require("jsonwebtoken");
const { initializeDatabase } = require("./database");
const authRoutes = require("./routes/auth");
const titleRoutes = require("./routes/titles");
const paintingRoutes = require("./routes/paintings");
const referenceRoutes = require("./routes/references");
require("dotenv").config();

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
  },
});
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ extended: true, limit: "50mb" }));
app.use("/uploads", express.static(path.join(__dirname, "uploads")));

// Store user socket connections
const userSockets = new Map();

// WebSocket authentication middleware
io.use((socket, next) => {
  const token = socket.handshake.auth.token;
  if (!token) {
    return next(new Error("Authentication error"));
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    socket.userId = decoded.id;
    next();
  } catch (err) {
    next(new Error("Authentication error"));
  }
});

// WebSocket connection handling
io.on("connection", (socket) => {
  console.log(`User ${socket.userId} connected via WebSocket`);

  // Store user socket connection
  userSockets.set(socket.userId, socket);

  socket.on("disconnect", () => {
    console.log(`User ${socket.userId} disconnected`);
    userSockets.delete(socket.userId);
  });
});

// Make io and userSockets available to other modules
global.io = io;
global.userSockets = userSockets;

// Routes
app.use("/api/auth", authRoutes);
app.use("/api/titles", titleRoutes);
app.use("/api/paintings", paintingRoutes);
app.use("/api/references", referenceRoutes);

// Config endpoint to provide server information to frontend
app.get("/api/config", (req, res) => {
  res.json({
    serverIP: process.env.SERVER_IP,
    apiPort: process.env.PORT || 3000,
  });
});

// Root route
app.get("/", (req, res) => {
  res.json({ message: "AI Image Generator API" });
});

// Initialize database and start server
initializeDatabase()
  .then(() => {
    server.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
      console.log(`WebSocket server ready`);
    });
  })
  .catch((err) => {
    console.error("Failed to start server:", err);
    process.exit(1);
  });
