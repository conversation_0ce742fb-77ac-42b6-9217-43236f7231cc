# Deployment Guide

Deploy your Painting Generator application to server `***************`.

## Quick Deployment (Automated)

1. **Make the deployment script executable:**
   ```bash
   chmod +x deploy.sh
   ```

2. **Run the deployment script:**
   ```bash
   ./deploy.sh
   ```

## Manual Deployment

### Step 1: Prepare Your Local Environment

1. **Make scripts executable:**
   ```bash
   chmod +x *.sh
   ```

### Step 2: Set Up the Server

1. **Connect to the server:**
   ```bash
   ssh root@***************
   # Password: 000000AA
   ```

2. **Run the server setup script:**
   ```bash
   # Copy server-setup.sh to the server first
   # Then run:
   bash server-setup.sh
   ```

### Step 3: Upload Application Files

1. **From your local machine, upload files:**
   ```bash
   ./upload-files.sh
   ```

   Or manually:
   ```bash
   scp -r . root@***************:/opt/painting-generator/
   ```

### Step 4: Configure and Start the Application

1. **SSH into the server:**
   ```bash
   ssh root@***************
   cd /opt/painting-generator
   ```

2. **Install dependencies:**
   ```bash
   npm install --production
   ```

3. **Set up environment:**
   ```bash
   cp .env.production .env
   # Edit .env and add your API keys
   nano .env
   ```

4. **Start MySQL database:**
   ```bash
   docker-compose up -d mysql
   ```

5. **Wait for MySQL to be ready (30 seconds), then start the app:**
   ```bash
   sleep 30
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

## Verification

1. **Check if services are running:**
   ```bash
   docker ps                    # Should show MySQL container
   pm2 status                   # Should show painting-generator app
   ```

2. **Test the application:**
   ```bash
   curl http://***************:3000
   ```

3. **Check logs:**
   ```bash
   pm2 logs painting-generator
   docker-compose logs mysql
   ```

## Application URLs

- **API Base:** `http://***************:3000`
- **Health Check:** `http://***************:3000/api/config`

## Important Configuration

### Environment Variables (.env)
```env
PORT=3000
DB_HOST=mysql                    # Important: Use 'mysql' for Docker
DB_USER=root
DB_PASSWORD=
DB_NAME=painting_generator
OPENROUTER_API_KEY=your_key_here # Add your actual API key
OPENAI_API_KEY=your_key_here     # Add your actual API key
JWT_SECRET=change_this_secret    # Use a strong secret
SERVER_IP=***************
```

## Troubleshooting

### MySQL Connection Issues
```bash
# Check if MySQL is running
docker ps
docker-compose logs mysql

# Restart MySQL
docker-compose restart mysql
```

### Application Issues
```bash
# Check PM2 status
pm2 status
pm2 logs painting-generator

# Restart application
pm2 restart painting-generator
```

### Port Issues
```bash
# Check what's using port 3000
netstat -tulpn | grep 3000

# Check firewall
ufw status
```

## Maintenance Commands

### Update Application
```bash
# Stop app
pm2 stop painting-generator

# Upload new files
# (use upload-files.sh from local machine)

# Restart app
pm2 start painting-generator
```

### Database Backup
```bash
docker exec painting_generator_mysql mysqldump -u root painting_generator > backup.sql
```

### View Logs
```bash
pm2 logs painting-generator
docker-compose logs mysql
```

## Security Notes

1. **Change default passwords** in production
2. **Use strong JWT secrets**
3. **Keep API keys secure**
4. **Consider setting up SSL/HTTPS**
5. **Regular security updates**

## Support

If you encounter issues:
1. Check the logs: `pm2 logs painting-generator`
2. Verify MySQL is running: `docker ps`
3. Check firewall settings: `ufw status`
4. Ensure all environment variables are set correctly
