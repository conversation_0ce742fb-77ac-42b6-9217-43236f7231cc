#!/bin/bash

# Script to upload application files to the server
# Run this from your local machine

SERVER="root@***************"
APP_DIR="/opt/painting-generator"

echo "=== Uploading files to server ==="

# Create a temporary directory with only the files we need
echo "Preparing files for upload..."
mkdir -p temp-deploy

# Copy necessary files (excluding node_modules and other unnecessary files)
cp -r controllers temp-deploy/
cp -r frontend temp-deploy/
cp -r middleware temp-deploy/
cp -r routes temp-deploy/
cp -r services temp-deploy/
cp -r uploads temp-deploy/
cp *.js temp-deploy/
cp *.json temp-deploy/
cp *.yml temp-deploy/
cp *.yaml temp-deploy/
cp *.sql temp-deploy/
cp *.css temp-deploy/
cp *.html temp-deploy/
cp .env temp-deploy/
cp *.md temp-deploy/ 2>/dev/null || true

# Update .env for production
echo "Updating .env for production..."
sed -i.bak 's/DB_HOST=localhost/DB_HOST=mysql/' temp-deploy/.env
sed -i.bak 's/SERVER_IP=/SERVER_IP=***************/' temp-deploy/.env

echo "Files prepared. Uploading to server..."

# Upload files using SCP
scp -r temp-deploy/* $SERVER:$APP_DIR/

echo "Upload completed!"

# Clean up
rm -rf temp-deploy

echo ""
echo "Next steps on the server:"
echo "1. ssh $SERVER"
echo "2. cd $APP_DIR"
echo "3. npm install --production"
echo "4. docker-compose up -d mysql"
echo "5. pm2 start ecosystem.config.js"
echo ""
echo "Or run the automated setup:"
echo "ssh $SERVER 'cd $APP_DIR && npm install --production && docker-compose up -d mysql && pm2 start ecosystem.config.js'"
