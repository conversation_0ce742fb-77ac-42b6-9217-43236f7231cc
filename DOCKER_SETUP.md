# Docker MySQL Setup

This project includes a Docker Compose configuration to run MySQL in a container.

## Files Created

- `docker-compose.yml` - Docker Compose configuration for MySQL
- `init.sql` - Database initialization script (automatically creates tables)
- Updated `.env` - Added Docker configuration option

## Quick Start

1. **Start the MySQL container:**
   ```bash
   docker-compose up -d mysql
   ```

2. **Update your .env file for Docker:**
   ```bash
   # Comment out the localhost line and uncomment the Docker line
   # DB_HOST=localhost
   DB_HOST=mysql
   ```

3. **Start your Node.js application:**
   ```bash
   npm start
   ```

## Docker Commands

- **Start MySQL container:** `docker-compose up -d mysql`
- **Stop MySQL container:** `docker-compose down`
- **View logs:** `docker-compose logs mysql`
- **Access MySQL CLI:** `docker-compose exec mysql mysql -u root -p painting_generator`

## Configuration Details

- **MySQL Version:** 8.0
- **Port:** 3306 (mapped to host)
- **Database:** painting_generator
- **User:** root (no password for development)
- **Data Persistence:** Uses Docker volume `mysql_data`

## Database Schema

The `init.sql` file automatically creates all required tables:
- users
- titles
- references2
- ideas
- paintings

## Switching Between Local and Docker MySQL

### For Docker MySQL:
```env
DB_HOST=mysql
```

### For Local MySQL:
```env
DB_HOST=localhost
```

## Troubleshooting

1. **Port 3306 already in use:** Stop your local MySQL service or change the port mapping in docker-compose.yml
2. **Connection refused:** Make sure the container is running with `docker-compose ps`
3. **Database not found:** The init.sql script should create it automatically, but you can create it manually if needed

## Data Persistence

Your database data is stored in a Docker volume named `mysql_data`. This means your data will persist even if you stop and restart the container.

To completely reset the database:
```bash
docker-compose down -v  # This removes volumes too
docker-compose up -d mysql
```
